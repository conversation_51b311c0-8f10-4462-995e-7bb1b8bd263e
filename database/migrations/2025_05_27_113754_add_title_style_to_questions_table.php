<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->string('title_style')->nullable()->comment('标题样式')->after('title');
            $table->string('description_style')->nullable()->comment('选项样式')->after('title_style');
            $table->string('option_style')->nullable()->comment('选项样式')->after('description_style');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->dropColumn('title_style');
            $table->dropColumn('description_style');
        });
    }
};
