<?php

use App\Enums\ReminderSendStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_reminder_settings', function (Blueprint $table) {
            // 发送状态：pending(待发送), success(成功), failed(失败)
            $table->enum('send_status', ReminderSendStatus::values())
                ->default(ReminderSendStatus::PENDING->value)
                ->after('last_reminded_at')
                ->comment('发送状态');

            // 失败原因
            $table->text('failure_reason')
                ->nullable()
                ->after('send_status')
                ->comment('发送失败原因');

            // 发送尝试次数
            $table->unsignedTinyInteger('send_attempts')
                ->default(0)
                ->after('failure_reason')
                ->comment('发送尝试次数');

            // 最后一次发送尝试时间
            $table->timestamp('last_send_at')
                ->nullable()
                ->after('send_attempts')
                ->comment('最后一次发送尝试时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_reminder_settings', function (Blueprint $table) {
            $table->dropColumn(['send_status', 'failure_reason', 'send_attempts', 'last_send_at']);
        });
    }
};
