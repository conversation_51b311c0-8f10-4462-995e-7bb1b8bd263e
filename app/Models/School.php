<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class School extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'code',
        'xueduan',
        'province',
        'area',
        'addr',
        'postcode',
        'tel',
        'source',
        'last_updated_at',
    ];

    /**
     * 属性转换
     */
    protected $casts = [
        'last_updated_at' => 'datetime',
    ];

    /**
     * 学段枚举
     */
    public const XUEDUAN_TYPES = [
        '幼儿园' => '幼儿园',
        '小学' => '小学',
        '初级中学' => '初级中学',
        '高级中学' => '高级中学',
        '九年一贯制学校' => '九年一贯制学校',
        '十二年一贯制学校' => '十二年一贯制学校',
        '完全中学' => '完全中学',
    ];

    /**
     * 根据省份查询学校
     */
    public function scopeByProvince($query, $province)
    {
        return $query->where('province', $province);
    }

    /**
     * 根据学段查询学校
     */
    public function scopeByXueduan($query, $xueduan)
    {
        return $query->where('xueduan', $xueduan);
    }

    /**
     * 根据地区查询学校
     */
    public function scopeByArea($query, $area)
    {
        return $query->where('area', 'like', "%{$area}%");
    }

    /**
     * 根据学校名称搜索
     */
    public function scopeByName($query, $name)
    {
        return $query->where('name', 'like', "%{$name}%");
    }
}
