<?php

namespace App\Packages;

use App\Models\School;
use App\Services\SchoolInfoService;
use Illuminate\Support\Facades\Log;

class SchoolInfoPackage
{
    /**
     * 学校信息服务实例
     */
    protected SchoolInfoService $schoolInfoService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->schoolInfoService = new SchoolInfoService();
    }

    /**
     * 快速查询学校信息
     *
     * @param array $params 查询参数
     * @return array
     */
    public static function query(array $params = []): array
    {
        $instance = new static();
        return $instance->schoolInfoService->querySchools($params);
    }

    /**
     * 快速保存学校信息
     *
     * @param array $schoolData 学校数据
     * @return School
     */
    public static function save(array $schoolData): School
    {
        $instance = new static();
        return $instance->schoolInfoService->saveSchool($schoolData);
    }

    /**
     * 批量采集指定省份的中小学信息
     *
     * @param string $province 省份名称
     * @param array $options 选项配置
     * @return array 采集结果
     */
    public static function collectByProvince(string $province, array $options = []): array
    {
        $instance = new static();
        
        // 默认配置
        $defaultOptions = [
            'xueduan' => ['小学', '初级中学'], // 默认采集中小学
            'max_pages' => 10, // 最大页数
            'log_progress' => true, // 是否记录进度日志
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        if ($options['log_progress']) {
            Log::info("开始采集学校信息", [
                'province' => $province,
                'xueduan' => $options['xueduan'],
                'max_pages' => $options['max_pages'],
            ]);
        }
        
        $results = $instance->schoolInfoService->collectSchoolsByProvince(
            $province,
            $options['xueduan'],
            $options['max_pages']
        );
        
        if ($options['log_progress']) {
            Log::info("学校信息采集完成", [
                'province' => $province,
                'results' => $results,
            ]);
        }
        
        return $results;
    }

    /**
     * 搜索本地数据库中的学校
     *
     * @param array $criteria 搜索条件
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function search(array $criteria = [])
    {
        $query = School::query();

        if (isset($criteria['province'])) {
            $query->byProvince($criteria['province']);
        }

        if (isset($criteria['area'])) {
            $query->byArea($criteria['area']);
        }

        if (isset($criteria['xueduan'])) {
            $query->byXueduan($criteria['xueduan']);
        }

        if (isset($criteria['name'])) {
            $query->byName($criteria['name']);
        }

        if (isset($criteria['limit'])) {
            $query->limit($criteria['limit']);
        }

        return $query->get();
    }

    /**
     * 获取统计信息
     *
     * @return array
     */
    public static function getStatistics(): array
    {
        $instance = new static();
        return $instance->schoolInfoService->getStatistics();
    }

    /**
     * 验证AppCode配置
     *
     * @return bool
     */
    public static function validateConfig(): bool
    {
        $appCode = config('services.aliyun.school_api.app_code');
        
        if (empty($appCode)) {
            Log::warning('学校信息API的AppCode未配置');
            return false;
        }
        
        return true;
    }

    /**
     * 测试API连接
     *
     * @return array
     */
    public static function testConnection(): array
    {
        try {
            if (!static::validateConfig()) {
                return [
                    'success' => false,
                    'message' => 'AppCode配置缺失',
                ];
            }

            $instance = new static();
            $response = $instance->schoolInfoService->querySchools([
                'province' => '北京',
                'xueduan' => '小学',
                'page' => 1,
            ]);

            return [
                'success' => true,
                'message' => 'API连接正常',
                'sample_data' => $response,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'API连接失败：' . $e->getMessage(),
            ];
        }
    }
}
