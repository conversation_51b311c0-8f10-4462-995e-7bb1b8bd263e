<?php

namespace App\Packages;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DomPdfPackage
{
    /**
     * 生成PDF并保存到存储中
     *
     * @param  string  $view  视图名称
     * @param  array  $data  视图数据
     * @param  string|null  $fileName  文件名
     * @return string 文件URL
     * @throws \Exception
     */
    public static function get($view, $data, $fileName = null)
    {
        try {
            // 确保目录存在
            self::ensureDirectoriesExist();

            // 获取PDF实例
            $pdf = self::getPdfInstance();

            // 设置文件名
            if (! $fileName) {
                $fileName = Str::uuid().'.pdf';
            }

            if (! str_ends_with($fileName, '.pdf')) {
                $fileName .= '.pdf';
            }

            $filePath = 'pdf/'.$fileName;

            // 渲染视图并确保编码正确
            $html = view($view, $data)->render();
            $html = mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8');

            // 加载HTML并生成PDF
            $pdf->loadHTML($html, 'UTF-8');
            $pdf->setPaper('A4', 'portrait');

            // 保存文件
            Storage::put($filePath, $pdf->output());

            return Storage::url($filePath);
        } catch (\Exception $e) {
            Log::error('PDF生成失败', [
                'error'    => $e->getMessage(),
                'trace'    => $e->getTraceAsString(),
                'view'     => $view,
                'fileName' => $fileName ?? 'unknown'
            ]);

            throw new \Exception('PDF生成失败: '.$e->getMessage());
        }
    }

    /**
     * 确保必要的目录存在
     */
    protected static function ensureDirectoriesExist()
    {
        $directories = [
            storage_path('fonts'),
            storage_path('fonts/cache'),
            storage_path('fonts/source'),
            storage_path('fonts/register'),
            storage_path('temp')
        ];

        foreach ($directories as $dir) {
            if (! is_dir($dir)) {
                File::makeDirectory($dir, 0755, true);
            }
        }

        // 确保字体配置文件存在于注册目录
        $registerDir = storage_path('fonts/register');
        $configFile  = $registerDir.'/installed-fonts.json';

        if (! file_exists($configFile)) {
            $defaultFonts = [
                "msyh"   => [
                    "normal"      => "msyh.ttf",
                    "bold"        => "msyh.ttf",
                    "italic"      => "msyh.ttf",
                    "bold_italic" => "msyh.ttf"
                ],
                "simhei" => [
                    "normal"      => "simhei.ttf",
                    "bold"        => "simhei.ttf",
                    "italic"      => "simhei.ttf",
                    "bold_italic" => "simhei.ttf"
                ]
            ];

            file_put_contents($configFile, json_encode($defaultFonts, JSON_PRETTY_PRINT));
        }
    }

    /**
     * 获取配置好的PDF实例
     */
    protected static function getPdfInstance()
    {
        // 确保字体配置文件存在
        self::ensureFontConfigFilesExist();

        $pdf = app('dompdf.wrapper');

        // 创建忽略SSL验证的上下文
        $context = stream_context_create([
            'ssl'  => [
                'verify_peer'       => false,
                'verify_peer_name'  => false,
                'allow_self_signed' => true
            ],
            'http' => [
                'timeout'    => 60,
                'user_agent' => 'Mozilla/5.0 (compatible; DOMPDF)'
            ]
        ]);

        // 配置选项
        $options = $pdf->getOptions();
        $options->set([
            'defaultFont'             => 'msyh',
            'isRemoteEnabled'         => true,
            'isHtml5ParserEnabled'    => true,
            'isFontSubsettingEnabled' => false,
            'defaultMediaType'        => 'screen',
            'logOutputFile'           => storage_path('logs/dompdf.html'),
            'tempDir'                 => storage_path('temp/'),
            'fontDir'                 => storage_path('fonts/register'),
            'fontCache'               => storage_path('fonts/cache')
        ]);

        // 设置HTTP上下文
        $pdf->getDomPDF()->setHttpContext($context);

        return $pdf;
    }

    /**
     * 确保字体配置文件存在
     */
    protected static function ensureFontConfigFilesExist()
    {
        $distFilePath  = base_path('vendor/dompdf/dompdf/lib/fonts/installed-fonts.dist.json');
        $fontsFilePath = base_path('vendor/dompdf/dompdf/lib/fonts/installed-fonts.json');

        // 确保目录存在
        $fontDir = dirname($distFilePath);
        if (! is_dir($fontDir)) {
            mkdir($fontDir, 0755, true);
        }

        // 创建 installed-fonts.dist.json 如果不存在
        if (! file_exists($distFilePath)) {
            $defaultFonts = [
                "dejavu sans"      => [
                    "normal"      => "DejaVuSans.ttf",
                    "bold"        => "DejaVuSans-Bold.ttf",
                    "italic"      => "DejaVuSans-Oblique.ttf",
                    "bold_italic" => "DejaVuSans-BoldOblique.ttf"
                ],
                "dejavu serif"     => [
                    "normal"      => "DejaVuSerif.ttf",
                    "bold"        => "DejaVuSerif-Bold.ttf",
                    "italic"      => "DejaVuSerif-Italic.ttf",
                    "bold_italic" => "DejaVuSerif-BoldItalic.ttf"
                ],
                "dejavu sans mono" => [
                    "normal"      => "DejaVuSansMono.ttf",
                    "bold"        => "DejaVuSansMono-Bold.ttf",
                    "italic"      => "DejaVuSansMono-Oblique.ttf",
                    "bold_italic" => "DejaVuSansMono-BoldOblique.ttf"
                ],
                "msyh"             => [
                    "normal"      => "msyh.ttf",
                    "bold"        => "msyh.ttf",
                    "italic"      => "msyh.ttf",
                    "bold_italic" => "msyh.ttf"
                ],
                "simhei"           => [
                    "normal"      => "simhei.ttf",
                    "bold"        => "simhei.ttf",
                    "italic"      => "simhei.ttf",
                    "bold_italic" => "simhei.ttf"
                ]
            ];

            file_put_contents($distFilePath, json_encode($defaultFonts, JSON_PRETTY_PRINT));
        }

        // 创建 installed-fonts.json 如果不存在
        if (! file_exists($fontsFilePath) && file_exists($distFilePath)) {
            copy($distFilePath, $fontsFilePath);
        } else {
            if (file_exists($fontsFilePath)) {
                // 确保 installed-fonts.json 包含中文字体配置
                $fonts    = json_decode(file_get_contents($fontsFilePath), true);
                $modified = false;

                // 检查是否有空键
                if (isset($fonts[''])) {
                    unset($fonts['']);
                    $modified = true;
                }

                if (! isset($fonts['msyh'])) {
                    $fonts['msyh'] = [
                        "normal"      => "msyh.ttf",
                        "bold"        => "msyh.ttf",
                        "italic"      => "msyh.ttf",
                        "bold_italic" => "msyh.ttf"
                    ];
                    $modified      = true;
                }

                if (! isset($fonts['simhei'])) {
                    $fonts['simhei'] = [
                        "normal"      => "simhei.ttf",
                        "bold"        => "simhei.ttf",
                        "italic"      => "simhei.ttf",
                        "bold_italic" => "simhei.ttf"
                    ];
                    $modified        = true;
                }

                if ($modified) {
                    file_put_contents($fontsFilePath, json_encode($fonts, JSON_PRETTY_PRINT));
                }
            }
        }

        // 复制字体文件到vendor目录
        $sourceDir     = storage_path('fonts/source');
        $vendorFontDir = base_path('vendor/dompdf/dompdf/lib/fonts');

        if (is_dir($sourceDir)) {
            $fontFiles = array_merge(
                glob($sourceDir."/*.ttf") ?: [],
                glob($sourceDir."/*.otf") ?: [],
                glob($sourceDir."/*.ttc") ?: []
            );

            if (! empty($fontFiles)) {
                foreach ($fontFiles as $fontFile) {
                    $fileName = basename($fontFile);
                    $destFile = $vendorFontDir.'/'.$fileName;

                    // 如果目标文件不存在或源文件较新，则复制
                    if (! file_exists($destFile) || filemtime($fontFile) > filemtime($destFile)) {
                        copy($fontFile, $destFile);
                    }
                }
            }
        }
    }

    /**
     * Notes: 优化版本
     *
     * @Author: 玄尘
     * @Date: 2025/5/26 19:55
     * @param $view
     * @param $data
     * @param $fileName
     * @return string
     * @throws \Throwable
     */
    public static function getOptimized($view, $data, $fileName = null)
    {
        try {
            $pdf = app('dompdf.wrapper');

            // 创建忽略SSL验证的上下文
            $context = stream_context_create([
                'ssl'  => [
                    'verify_peer'       => false,
                    'verify_peer_name'  => false,
                    'allow_self_signed' => true
                ],
                'http' => [
                    'timeout'    => 60,
                    'user_agent' => 'Mozilla/5.0 (compatible; DOMPDF)'
                ]
            ]);

            // 优化的DOMPDF选项
            $options = $pdf->getOptions();
            $options->set([
                'defaultFont'             => 'msyh',
                'isRemoteEnabled'         => true,
                'isHtml5ParserEnabled'    => true,
                'isFontSubsettingEnabled' => true, // 启用字体子集化减少内存
                'defaultMediaType'        => 'screen',
                'logOutputFile'           => storage_path('logs/dompdf.html'),
                'tempDir'                 => storage_path('temp/'),
                'fontCache'               => storage_path('fonts/cache'),
                'chroot'                  => public_path(), // 限制文件访问范围
                'debugKeepTemp'           => false, // 不保留临时文件
                'debugCss'                => false, // 关闭CSS调试
                'debugLayout'             => false, // 关闭布局调试
                'debugLayoutLines'        => false, // 关闭布局线调试
                'debugLayoutBlocks'       => false, // 关闭布局块调试
                'debugLayoutInline'       => false, // 关闭内联布局调试
                'debugLayoutPaddingBox'   => false, // 关闭内边距盒调试
            ]);

            // 设置HTTP上下文
            $pdf->getDomPDF()->setHttpContext($context);

            if (! $fileName) {
                $fileName = Str::uuid().'.pdf';
            }

            if (! str_ends_with($fileName, '.pdf')) {
                $fileName .= '.pdf';
            }

            $filePath = 'pdf/'.$fileName;

            // 渲染视图并确保编码正确
            $html = view($view, $data)->render();

            // 清理不必要的HTML以减少内存使用
            $html = preg_replace('/<!--(.|\s)*?-->/', '', $html); // 移除HTML注释
            $html = preg_replace('/\s{2,}/', ' ', $html);         // 压缩多余空白

            // 确保HTML内容是UTF-8编码
            $html = mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8');

            // 加载HTML并生成PDF
            $pdf->loadHTML($html, 'UTF-8');
            $pdf->setPaper('A4', 'portrait');

            // 保存文件
            Storage::put($filePath, $pdf->output());

            // 手动触发垃圾回收
            unset($html, $pdf);
            gc_collect_cycles();

            return Storage::url($filePath);
        } catch (\Exception $e) {
            \Log::error('PDF生成失败', [
                'error'    => $e->getMessage(),
                'fileName' => $fileName ?? 'unknown'
            ]);
            throw $e;
        }
    }
}
