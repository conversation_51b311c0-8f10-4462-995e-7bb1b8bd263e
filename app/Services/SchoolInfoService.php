<?php

namespace App\Services;

use App\Models\School;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class SchoolInfoService
{
    /**
     * API端点URL
     */
    protected string $apiUrl = 'https://qryschool.market.alicloudapi.com/lundroid/schools';

    /**
     * GuzzleHttp客户端
     */
    protected Client $client;

    /**
     * AppCode
     */
    protected string $appCode;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->client = new Client([
            'timeout' => 30,
        ]);
        
        $this->appCode = config('services.aliyun.school_api.app_code', '');
    }

    /**
     * 查询学校信息
     *
     * @param array $params 查询参数
     * @return array
     * @throws \Exception
     */
    public function querySchools(array $params = []): array
    {
        try {
            // 构建查询参数
            $queryParams = $this->buildQueryParams($params);
            
            // 构建完整URL
            $url = $this->apiUrl . '?' . http_build_query($queryParams);
            
            // 设置请求头
            $headers = [
                'Authorization' => 'APPCODE ' . $this->appCode,
                'Accept' => 'application/json',
            ];

            // 发送请求
            $response = $this->client->request('GET', $url, [
                'headers' => $headers,
            ]);

            // 解析响应
            $statusCode = $response->getStatusCode();
            $body = json_decode($response->getBody()->getContents(), true);

            if ($statusCode !== 200) {
                throw new \Exception('API请求失败，状态码：' . $statusCode);
            }

            if (!isset($body['resp']) || $body['resp']['code'] !== 200) {
                throw new \Exception('API返回错误：' . ($body['resp']['desc'] ?? '未知错误'));
            }

            return $body;
        } catch (GuzzleException $e) {
            Log::error('学校信息查询API调用失败', [
                'error' => $e->getMessage(),
                'params' => $params,
            ]);
            throw new \Exception('学校信息查询失败：' . $e->getMessage());
        }
    }

    /**
     * 保存学校信息到数据库
     *
     * @param array $schoolData 学校数据
     * @return School
     */
    public function saveSchool(array $schoolData): School
    {
        return School::updateOrCreate(
            ['code' => $schoolData['code']],
            [
                'name' => $schoolData['name'],
                'xueduan' => $schoolData['xueduan'],
                'province' => $schoolData['province'],
                'area' => $schoolData['area'],
                'addr' => $schoolData['addr'],
                'postcode' => $schoolData['postcode'] ?? null,
                'tel' => $schoolData['tel'] ?? null,
                'source' => 'aliyun_api',
                'last_updated_at' => now(),
            ]
        );
    }

    /**
     * 批量保存学校信息
     *
     * @param array $schoolsData 学校数据数组
     * @return array 保存结果
     */
    public function batchSaveSchools(array $schoolsData): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        foreach ($schoolsData as $schoolData) {
            try {
                $this->saveSchool($schoolData);
                $results['success']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'school' => $schoolData['name'] ?? '未知',
                    'error' => $e->getMessage(),
                ];
                
                Log::error('保存学校信息失败', [
                    'school_data' => $schoolData,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $results;
    }

    /**
     * 根据省份采集学校信息
     *
     * @param string $province 省份名称
     * @param array $xueduan 学段数组，默认为中小学
     * @param int $maxPages 最大页数
     * @return array 采集结果
     */
    public function collectSchoolsByProvince(string $province, array $xueduan = ['小学', '初级中学'], int $maxPages = 10): array
    {
        $allResults = [
            'total_collected' => 0,
            'success' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        foreach ($xueduan as $xd) {
            $page = 1;
            
            do {
                try {
                    $response = $this->querySchools([
                        'province' => $province,
                        'xueduan' => $xd,
                        'page' => $page,
                    ]);

                    if (isset($response['data']) && !empty($response['data'])) {
                        $results = $this->batchSaveSchools($response['data']);
                        
                        $allResults['total_collected'] += count($response['data']);
                        $allResults['success'] += $results['success'];
                        $allResults['failed'] += $results['failed'];
                        $allResults['errors'] = array_merge($allResults['errors'], $results['errors']);
                        
                        Log::info("采集学校信息", [
                            'province' => $province,
                            'xueduan' => $xd,
                            'page' => $page,
                            'count' => count($response['data']),
                        ]);
                    }

                    // 检查是否还有更多页
                    $hasMorePages = isset($response['resp']['total']) && 
                                   $page * 20 < $response['resp']['total'] && 
                                   $page < $maxPages;
                    
                    $page++;
                } catch (\Exception $e) {
                    Log::error("采集学校信息失败", [
                        'province' => $province,
                        'xueduan' => $xd,
                        'page' => $page,
                        'error' => $e->getMessage(),
                    ]);
                    
                    $allResults['errors'][] = [
                        'province' => $province,
                        'xueduan' => $xd,
                        'page' => $page,
                        'error' => $e->getMessage(),
                    ];
                    
                    break;
                }
            } while ($hasMorePages ?? false);
        }

        return $allResults;
    }

    /**
     * 构建查询参数
     *
     * @param array $params 输入参数
     * @return array 查询参数
     */
    protected function buildQueryParams(array $params): array
    {
        $queryParams = [];

        // 支持的查询参数
        $allowedParams = ['name', 'province', 'addr', 'xueduan', 'code', 'page'];
        
        foreach ($allowedParams as $param) {
            if (isset($params[$param]) && !empty($params[$param])) {
                $queryParams[$param] = $params[$param];
            }
        }

        // 默认页码
        if (!isset($queryParams['page'])) {
            $queryParams['page'] = 1;
        }

        return $queryParams;
    }

    /**
     * 获取支持的学段类型
     *
     * @return array
     */
    public function getSupportedXueduan(): array
    {
        return ['幼儿园', '小学', '初级中学', '高级中学'];
    }

    /**
     * 获取统计信息
     *
     * @return array
     */
    public function getStatistics(): array
    {
        return [
            'total_schools' => School::count(),
            'by_province' => School::selectRaw('province, COUNT(*) as count')
                ->groupBy('province')
                ->orderBy('count', 'desc')
                ->get()
                ->toArray(),
            'by_xueduan' => School::selectRaw('xueduan, COUNT(*) as count')
                ->groupBy('xueduan')
                ->orderBy('count', 'desc')
                ->get()
                ->toArray(),
            'last_updated' => School::max('last_updated_at'),
        ];
    }
}
