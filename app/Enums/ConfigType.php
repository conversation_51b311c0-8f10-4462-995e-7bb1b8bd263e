<?php

namespace App\Enums;

use App\Traits\EnumMethods;

enum ConfigType: string
{
    use EnumMethods;

    case ARRAY           = 'array';
    case CHECKBOX        = 'checkbox';
    case DIVIDER         = 'divider';
    case FILE            = 'file';
    case IMAGE           = 'image';
    case MULTIPLE_FILE   = 'multiple_file';
    case MULTIPLE_IMAGE  = 'multiple_image';
    case MULTIPLE_SELECT = 'multiple_select';
    case NUMBER          = 'number';
    case PASSWORD        = 'password';
    case RADIO           = 'radio';
    case RATE            = 'rate';
    case SELECT          = 'select';
    case STRING          = 'string';
    case TABS            = 'tabs';
    case URL             = 'url';

    const TYPE_MAP = [
        self::ARRAY->value           => '数组',
        self::CHECKBOX->value        => '多选',
        self::DIVIDER->value         => '分割线',
        self::FILE->value            => '文件上传',
        self::IMAGE->value           => '图片上传',
        self::MULTIPLE_FILE->value   => '多文件上传',
        self::MULTIPLE_IMAGE->value  => '轮播图上传',
        self::MULTIPLE_SELECT->value => '下拉多选器',
        self::NUMBER->value          => '数字输入',
        self::PASSWORD->value        => '密码输入',
        self::RADIO->value           => '单选',
        self::RATE->value            => '比例输入',
        self::SELECT->value          => '下拉选择器',
        self::STRING->value          => '字符输入',
        self::TABS->value            => 'TAB选择',
        self::URL->value             => 'URL输入',
    ];

    public function toString(): string
    {
        return self::TYPE_MAP[$this->value];
    }
}
