<?php

namespace App\Admin\Forms\Organization;

use App\Models\Organization;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class ReviewForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $organization = Organization::find($this->payload['id']);

        if ($organization->status != Organization::STATUS_INIT) {
            return $this->response()->error('当前状态无法操作');
        }

        switch ($input['result']) {
            case Organization::STATUS_PASS:
                $organization->pass(Admin::user());
                break;
            case Organization::STATUS_REJECT:
                $organization->reject(Admin::user(), $input['reason']);
        }

        return $this->response()->success('操作成功')->refresh();
    }

    public function form(): void
    {
        $this->radio('result', '审核结果')
            ->options([
                Organization::STATUS_PASS   => '审核通过',
                Organization::STATUS_REJECT => '驳回申请',
            ])
            ->default(Organization::STATUS_PASS)
            ->required()
            ->when(Organization::STATUS_REJECT, function () {
                $this->textarea('reason', '驳回原因');
            });
        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }

}