<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class DompdfManager extends Command
{
    protected $signature = 'dompdf:manager
                           {action=help : 要执行的操作 (help, check, fix, fonts, clean)}
                           {--force : 强制执行操作}
                           {--clean : 清理缓存后重新注册}';

    protected $description = 'DomPDF 管理工具 - 检查、修复和管理字体';

    protected $sourceDir;
    protected $cacheDir;
    protected $fontDir;

    public function handle(): int
    {
        // 初始化目录
        $this->sourceDir = storage_path('fonts/source');
        $this->cacheDir  = storage_path('fonts/cache');
        $this->fontDir   = storage_path('fonts/register');

        // 获取操作
        $action = $this->argument('action');

        // 执行相应的操作
        switch ($action) {
            case 'help':
                $this->showHelp();
                break;
            case 'check':
                $this->checkFonts();
                break;
            case 'fix':
                $this->fixFonts();
                break;
            case 'fonts':
                $this->registerFonts();
                break;
            case 'clean':
                $this->cleanCache();
                break;
            default:
                $this->error("未知操作: $action");
                $this->showHelp();
                return 1;
        }

        return 0;
    }

    /**
     * 显示帮助信息
     */
    protected function showHelp(): void
    {
        $this->info('DomPDF 管理工具 - 帮助信息');
        $this->line('');
        $this->line('可用操作:');
        $this->line('  help   - 显示此帮助信息');
        $this->line('  check  - 检查 DomPDF 字体状态');
        $this->line('  fix    - 修复 DomPDF 字体问题');
        $this->line('  fonts  - 注册字体');
        $this->line('  clean  - 清理字体缓存');
        $this->line('');
        $this->line('示例:');
        $this->line('  php artisan dompdf:manager check');
        $this->line('  php artisan dompdf:manager fix');
        $this->line('  php artisan dompdf:manager fonts');
        $this->line('  php artisan dompdf:manager clean');
    }

    /**
     * 检查字体状态
     */
    protected function checkFonts(): void
    {
        $this->info('检查 DomPDF 字体状态...');

        // 检查目录
        $this->checkDirectories();

        // 检查字体文件
        $this->checkFontFiles();

        // 检查配置
        $this->checkConfig();

        $this->info('✅ 检查完成');
    }

    /**
     * 修复字体问题
     */
    protected function fixFonts(): void
    {
        $this->info('修复 DomPDF 字体问题...');

        // 创建必要的目录
        $this->createRequiredDirectories();

        // 修改配置文件
        $this->updateConfig();

        // 确保字体文件存在
        $this->ensureFontsExist();

        $this->info('✅ 修复完成');
    }

    /**
     * 注册字体
     */
    protected function registerFonts(): void
    {
        $this->info('注册字体到 DomPDF...');

        // 清理缓存（如果需要）
        if ($this->option('clean') || $this->option('force')) {
            $this->cleanCache();
        }

        // 确保目录存在
        $this->createRequiredDirectories();

        // 确保字体配置文件存在
        $this->ensureFontConfigFilesExist();

        // 获取DomPDF实例
        $dompdf = app('dompdf.wrapper');

        // 获取字体文件
        $fontFiles = $this->getFontFiles();
        if (empty($fontFiles)) {
            $this->error('未找到字体文件!');
            $this->line('请将 .ttf, .otf 或 .ttc 字体文件放入源目录中: '.$this->sourceDir);
            return;
        }

        $this->info("找到 ".count($fontFiles)." 个字体文件");

        // 初始化进度条
        $this->output->progressStart(count($fontFiles));

        $successCount = 0;
        $errorCount   = 0;

        foreach ($fontFiles as $fontFile) {
            try {
                $this->processFont($fontFile, $dompdf);
                $successCount++;
            } catch (\Exception $e) {
                $errorCount++;
                if ($this->getOutput()->isVerbose()) {
                    $this->error("处理字体失败 [{$fontFile}]: {$e->getMessage()}");
                }
            }
            $this->output->progressAdvance();
        }

        $this->output->progressFinish();

        // 显示处理结果
        $this->newLine();
        $this->info("✅ 字体注册完成！成功: {$successCount}, 失败: {$errorCount}");
    }

    /**
     * 确保字体配置文件存在
     */
    protected function ensureFontConfigFilesExist(): void
    {
        $distFilePath = base_path('vendor/dompdf/dompdf/lib/fonts/installed-fonts.dist.json');
        $fontsFilePath = base_path('vendor/dompdf/dompdf/lib/fonts/installed-fonts.json');

        // 确保目录存在
        $fontDir = dirname($distFilePath);
        if (!is_dir($fontDir)) {
            mkdir($fontDir, 0755, true);
            $this->info("已创建字体库目录: {$fontDir}");
        }

        // 创建 installed-fonts.dist.json 如果不存在
        if (!file_exists($distFilePath)) {
            $defaultFonts = [
                "dejavu sans" => [
                    "normal" => "DejaVuSans.ttf",
                    "bold" => "DejaVuSans-Bold.ttf",
                    "italic" => "DejaVuSans-Oblique.ttf",
                    "bold_italic" => "DejaVuSans-BoldOblique.ttf"
                ],
                "dejavu serif" => [
                    "normal" => "DejaVuSerif.ttf",
                    "bold" => "DejaVuSerif-Bold.ttf",
                    "italic" => "DejaVuSerif-Italic.ttf",
                    "bold_italic" => "DejaVuSerif-BoldItalic.ttf"
                ],
                "dejavu sans mono" => [
                    "normal" => "DejaVuSansMono.ttf",
                    "bold" => "DejaVuSansMono-Bold.ttf",
                    "italic" => "DejaVuSansMono-Oblique.ttf",
                    "bold_italic" => "DejaVuSansMono-BoldOblique.ttf"
                ],
                "msyh" => [
                    "normal" => "msyh.ttf",
                    "bold" => "msyh.ttf",
                    "italic" => "msyh.ttf",
                    "bold_italic" => "msyh.ttf"
                ],
                "simhei" => [
                    "normal" => "simhei.ttf",
                    "bold" => "simhei.ttf",
                    "italic" => "simhei.ttf",
                    "bold_italic" => "simhei.ttf"
                ]
            ];

            file_put_contents($distFilePath, json_encode($defaultFonts, JSON_PRETTY_PRINT));
            $this->info("已创建字体配置文件: {$distFilePath}");
        }

        // 创建 installed-fonts.json 如果不存在
        if (!file_exists($fontsFilePath) && file_exists($distFilePath)) {
            copy($distFilePath, $fontsFilePath);
            $this->info("已创建字体配置文件: {$fontsFilePath}");
        } else {
            // 确保 installed-fonts.json 包含中文字体配置
            $fonts = json_decode(file_get_contents($fontsFilePath), true);
            $modified = false;

            if (!isset($fonts['msyh'])) {
                $fonts['msyh'] = [
                    "normal" => "msyh.ttf",
                    "bold" => "msyh.ttf",
                    "italic" => "msyh.ttf",
                    "bold_italic" => "msyh.ttf"
                ];
                $modified = true;
            }

            if (!isset($fonts['simhei'])) {
                $fonts['simhei'] = [
                    "normal" => "simhei.ttf",
                    "bold" => "simhei.ttf",
                    "italic" => "simhei.ttf",
                    "bold_italic" => "simhei.ttf"
                ];
                $modified = true;
            }

            if ($modified) {
                file_put_contents($fontsFilePath, json_encode($fonts, JSON_PRETTY_PRINT));
                $this->info("已更新字体配置文件: {$fontsFilePath}");
            }
        }
    }

    /**
     * 清理缓存
     */
    protected function cleanCache(): void
    {
        $this->info('清理字体缓存...');

        // 获取DomPDF实例
        $dompdf = app('dompdf.wrapper');

        // 清理缓存目录中的缓存文件
        if (is_dir($this->cacheDir)) {
            $files = File::glob($this->cacheDir.'/*.{ufm,php,afm}', GLOB_BRACE);
            $deletedCount = 0;

            foreach ($files as $file) {
                if (unlink($file)) {
                    $deletedCount++;
                }
            }

            $this->info("已从缓存目录删除 {$deletedCount} 个文件");
        }

        // 确保字体配置文件存在
        $this->ensureFontConfigFilesExist();

        // DomPDF没有直接的清除缓存方法，我们已经手动删除了缓存文件

        $this->info('✅ 缓存清理完成');
    }

    /**
     * 检查目录
     */
    protected function checkDirectories(): void
    {
        $directories = [
            '源文件目录' => $this->sourceDir,
            '缓存目录'   => $this->cacheDir,
            '字体目录'   => $this->fontDir
        ];

        $this->line("\n字体目录检查:");
        $this->table(
            ['目录类型', '路径', '存在', '可读', '可写', '权限'],
            collect($directories)->map(function ($dir, $type) {
                $exists      = file_exists($dir);
                $readable    = $exists && is_readable($dir);
                $writable    = $exists && is_writable($dir);
                $permissions = $exists ? substr(sprintf('%o', fileperms($dir)), -4) : 'N/A';

                return [
                    $type,
                    $dir,
                    $exists ? '✓' : '×',
                    $readable ? '✓' : '×',
                    $writable ? '✓' : '×',
                    $permissions
                ];
            })->toArray()
        );
    }

    /**
     * 检查字体文件
     */
    protected function checkFontFiles(): void
    {
        $extensions  = ['ttf', 'otf', 'ttc', 'woff', 'woff2'];
        $fontFiles   = [];
        $directories = [
            $this->sourceDir,
            $this->cacheDir,
            $this->fontDir
        ];

        foreach ($directories as $directory) {
            foreach ($extensions as $ext) {
                $files = glob($directory."/*.$ext");
                if ($files) {
                    $fontFiles = array_merge($fontFiles, $files);
                }
            }
        }

        // 去重
        $fontFiles = array_unique($fontFiles);

        if (empty($fontFiles)) {
            $this->warn('在所有目录中未找到任何字体文件');
            $this->line('支持的字体格式: '.implode(', ', $extensions));
            return;
        }

        $this->line("\n字体文件检查:");
        $this->table(
            ['文件名', '格式', '大小', '权限', '状态'],
            collect($fontFiles)->map(function ($file) {
                $fileName  = basename($file);
                $extension = strtoupper(pathinfo($file, PATHINFO_EXTENSION));
                $fileSize  = $this->formatBytes(filesize($file));
                $perms     = substr(sprintf('%o', fileperms($file)), -4);
                $status    = is_readable($file) ? '✓ 可读' : '× 不可读';

                return [$fileName, $extension, $fileSize, $perms, $status];
            })->toArray()
        );

        $this->info("总计找到 ".count($fontFiles)." 个字体文件");
    }

    /**
     * 检查配置
     */
    protected function checkConfig(): void
    {
        $configFile = config_path('dompdf.php');
        if (! file_exists($configFile)) {
            $this->error('配置文件不存在: '.$configFile);
            return;
        }

        $this->line("\nDomPDF 配置检查:");
        $this->table(
            ['配置项', '值', '状态'],
            [
                ['默认字体', config('dompdf.options.default_font'), '✓'],
                [
                    '字体目录', config('dompdf.options.font_dir'),
                    file_exists(config('dompdf.options.font_dir')) ? '✓' : '×'
                ],
                [
                    '字体缓存', config('dompdf.options.font_cache'),
                    file_exists(config('dompdf.options.font_cache')) ? '✓' : '×'
                ],
                ['远程内容', config('dompdf.options.enable_remote') ? '启用' : '禁用', '✓'],
                ['HTML5 解析器', config('dompdf.options.enable_html5_parser') ? '启用' : '禁用', '✓'],
            ]
        );
    }

    /**
     * 创建必要的目录
     */
    protected function createRequiredDirectories(): void
    {
        $directories = [
            $this->sourceDir,       // 源字体目录
            $this->cacheDir,        // 缓存目录
            $this->fontDir,         // 注册字体目录
            storage_path('fonts'),  // 字体根目录
            storage_path('fonts/cache'),  // 字体缓存目录
            storage_path('temp')    // 临时目录
        ];

        foreach ($directories as $dir) {
            if (! is_dir($dir)) {
                $this->info("创建目录: $dir");
                File::makeDirectory($dir, 0755, true, true);
            }

            // 确保目录有正确的权限
            chmod($dir, 0755);
        }
    }

    /**
     * 更新配置文件
     */
    protected function updateConfig(): void
    {
        // 检查配置文件是否存在
        $configFile = config_path('dompdf.php');
        if (! file_exists($configFile)) {
            $this->error('配置文件不存在: '.$configFile);
            return;
        }

        // 读取配置文件内容
        $config = file_get_contents($configFile);

        // 修改配置
        $config = preg_replace(
            "/'font_dir'\s*=>\s*storage_path\('fonts[^']*'\),/",
            "'font_dir' => storage_path('fonts/register'),",
            $config
        );

        $config = preg_replace(
            "/'font_cache'\s*=>\s*storage_path\('fonts[^']*'\),/",
            "'font_cache' => storage_path('fonts/cache'),",
            $config
        );

        // 写入配置文件
        file_put_contents($configFile, $config);
        $this->info('已更新配置文件: '.$configFile);

        // 清除配置缓存
        $this->call('config:clear');
    }

    /**
     * 确保字体文件存在
     */
    protected function ensureFontsExist(): void
    {
        // 检查默认字体是否存在
        $defaultFont     = config('dompdf.options.default_font');
        $defaultFontFile = $this->sourceDir.'/'.$defaultFont.'.ttf';

        if (! file_exists($defaultFontFile)) {
            $this->warn("默认字体文件不存在: $defaultFontFile");
            $this->line("请确保将字体文件放入源目录: {$this->sourceDir}");
        } else {
            $this->info("✓ 默认字体文件存在: $defaultFontFile");
        }
    }

    /**
     * 获取字体文件
     */
    protected function getFontFiles(): array
    {
        return array_merge(
            glob($this->sourceDir."/*.ttf") ?: [],
            glob($this->sourceDir."/*.otf") ?: [],
            glob($this->sourceDir."/*.ttc") ?: []
        );
    }

    /**
     * 处理字体
     */
    protected function processFont($fontFile, $dompdf): void
    {
        $fontName = pathinfo($fontFile, PATHINFO_FILENAME);
        $fileName = basename($fontFile);

        // 复制字体文件到注册目录
        $fontDirFile = $this->fontDir.'/'.$fileName;
        if (! file_exists($fontDirFile) || filemtime($fontFile) > filemtime($fontDirFile)) {
            copy($fontFile, $fontDirFile);
            $this->line("已复制字体文件到注册目录: {$fileName}");
        }

        // 复制字体文件到vendor目录
        $vendorFontDir = base_path('vendor/dompdf/dompdf/lib/fonts');
        $vendorFontFile = $vendorFontDir.'/'.$fileName;
        if (! file_exists($vendorFontFile) || filemtime($fontFile) > filemtime($vendorFontFile)) {
            copy($fontFile, $vendorFontFile);
            $this->line("已复制字体文件到vendor目录: {$fileName}");
        }

        // 注册字体到 DomPDF
        try {
            // 创建字体条目
            $styles = $this->createFontEntry($fontFile, $fontName);

            // 使用DomPDF的API注册字体
            foreach ($styles as $style) {
                $dompdf->getFontMetrics()->registerFont($style, $fontFile);
            }

            $this->line("已注册字体: {$fontName} (".count($styles)." 个变体)");
        } catch (\Exception $e) {
            throw new \Exception("注册字体 {$fontName} 失败: ".$e->getMessage());
        }
    }



    /**
     * 创建字体条目
     */
    protected function createFontEntry($fontFile, $fontName): array
    {
        // 创建字体条目
        $styles = [
            [
                'family' => $fontName,
                'style'  => 'normal',
                'weight' => 'normal'
            ]
        ];

        // 如果是中文字体，添加所有变体
        if (in_array($fontName, ['msyh', 'simhei', 'simsun', 'simkai', 'simfang'])) {
            $styles[] = [
                'family' => $fontName,
                'style'  => 'normal',
                'weight' => 'bold'
            ];

            $styles[] = [
                'family' => $fontName,
                'style'  => 'italic',
                'weight' => 'normal'
            ];

            $styles[] = [
                'family' => $fontName,
                'style'  => 'italic',
                'weight' => 'bold'
            ];
        }

        return $styles;
    }

    /**
     * 检查缓存是否有效
     */
    protected function isCacheValid($fontName): bool
    {
        // 检查缓存文件是否存在
        $cacheFile = $this->cacheDir.'/'.$fontName.'_normal.ufm';
        return file_exists($cacheFile);
    }

    /**
     * 格式化字节数
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow   = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow   = min($pow, count($units) - 1);

        return round($bytes / pow(1024, $pow), $precision).' '.$units[$pow];
    }
}
