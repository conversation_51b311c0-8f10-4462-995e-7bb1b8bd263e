<?php

namespace App\Console\Commands;

use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Promise;
use Illuminate\Console\Command;

class RealConcurrentHttpTest extends Command
{
    protected $signature = 'test:real-concurrent-http
                            {--server=https://sd.askok.cn : 服务器地址}
                            {--token= : Bearer认证Token}
                            {--concurrent=10 : 并发数}
                            {--requests=100 : 总请求数}
                            {--connect-timeout=5 : 连接超时时间(秒)}
                            {--request-timeout=30 : 请求超时时间(秒)}
                            {--method=guzzle : 并发方法(guzzle|curl)}
                            {--mode=batch : 并发模式(batch|full|progressive)}
                            {--retry-count=2 : 失败重试次数}
                            {--warmup=5 : 预热请求数}
                            {--save-report= : 保存详细报告到文件}';

    protected $description = '真正的并发HTTP压力测试 - 支持多种并发模式和详细性能分析';

    protected Client $httpClient;
    protected array  $testConfig;
    protected array  $performanceMetrics = [];

    public function handle()
    {
        $this->info('🚀 === 真正的并发HTTP压力测试 === 🚀');

        if (! $this->initializeConfig()) {
            return 1;
        }

        // 预热请求
        if ($this->testConfig['warmup'] > 0) {
            $this->runWarmupRequests();
        }

        $method = $this->option('method');
        $mode   = $this->option('mode');

        $this->info("🔧 测试配置: {$method}方法 + {$mode}模式");

        switch ($method) {
            case 'guzzle':
                $this->runGuzzleAsyncTest();
                break;
            case 'curl':
                $this->runCurlMultiTest();
                break;
            default:
                $this->error('不支持的并发方法');
                return 1;
        }

        // 保存详细报告
        if ($this->option('save-report')) {
            $this->saveDetailedReport();
        }

        return 0;
    }

    private function initializeConfig(): bool
    {
        $this->testConfig = [
            'server'          => rtrim($this->option('server'), '/'),
            'token'           => $this->option('token'),
            'concurrent'      => $this->option('concurrent'),
            'requests'        => $this->option('requests'),
            'connect_timeout' => $this->option('connect-timeout'),
            'request_timeout' => $this->option('request-timeout'),
            'mode'            => $this->option('mode'),
            'retry_count'     => $this->option('retry-count'),
            'warmup'          => $this->option('warmup'),
            'api_endpoint'    => '/api/analyses',
        ];

        $this->httpClient = new Client([
            'timeout'         => $this->testConfig['request_timeout'],
            'connect_timeout' => $this->testConfig['connect_timeout'],
            'verify'          => false,
            'pool_size'       => $this->testConfig['concurrent'] * 2, // 连接池优化
        ]);

        $this->info("⚙️  配置信息:");
        $this->info("   服务器: {$this->testConfig['server']}");
        $this->info("   并发数: {$this->testConfig['concurrent']}");
        $this->info("   总请求: {$this->testConfig['requests']}");
        $this->info("   连接超时: {$this->testConfig['connect_timeout']}秒");
        $this->info("   请求超时: {$this->testConfig['request_timeout']}秒");
        $this->info("   并发模式: {$this->testConfig['mode']}");

        return true;
    }

    /**
     * 方法1: 使用Guzzle异步请求实现真正并发
     */
    private function runGuzzleAsyncTest(): void
    {
        $this->info('🔥 使用Guzzle异步请求进行并发测试');

        $mode = $this->testConfig['mode'];

        switch ($mode) {
            case 'batch':
                $this->runBatchConcurrentTest();
                break;
            case 'full':
                $this->runFullConcurrentTest();
                break;
            case 'progressive':
                $this->runProgressiveConcurrentTest();
                break;
            default:
                $this->error('不支持的并发模式');
        }
    }

    /**
     * 分批并发测试 - 原有模式的优化版本
     */
    private function runBatchConcurrentTest(): void
    {
        $this->info('📦 分批并发模式 - 稳定性测试');

        $concurrent    = $this->testConfig['concurrent'];
        $totalRequests = $this->testConfig['requests'];
        $url           = $this->testConfig['server'].$this->testConfig['api_endpoint'];

        $startTime = microtime(true);
        $results   = [];

        // 分批处理，每批并发执行
        $batches = array_chunk(range(1, $totalRequests), $concurrent);

        foreach ($batches as $batchIndex => $batch) {
            $this->info("执行第".($batchIndex + 1)."批并发请求 (并发数: ".count($batch).")");

            $batchResults = $this->executeBatchRequests($batch, $url);
            $results      = array_merge($results, $batchResults);

            // 实时显示当前QPS
            $currentTime = microtime(true);
            $currentQPS  = count($results) / ($currentTime - $startTime);
            $this->info("当前QPS: ".round($currentQPS, 2));

            // 批次间隔
            if ($batchIndex < count($batches) - 1) {
                usleep(100000); // 100ms间隔
            }
        }

        $endTime       = microtime(true);
        $totalDuration = $endTime - $startTime;

        $this->analyzeResults($results, $totalDuration, 'Guzzle异步-分批');
    }

    /**
     * 全并发测试 - 所有请求同时发起
     */

    private function runFullConcurrentTest(): void
    {
        $this->info('🚀 全并发模式 - 峰值压力测试');

        $totalRequests = (int) $this->testConfig['requests'];
        $url           = $this->testConfig['server'].$this->testConfig['api_endpoint'];
        $concurrent    = (int) $this->testConfig['concurrent'];

        $startTime = microtime(true);
        $results   = [];

        // 生成请求生成器
        $requests = function ($total) use ($url) {
            for ($i = 1; $i <= $total; $i++) {
                yield function () use ($url, $i) {
                    return $this->httpClient->postAsync($url, [
                        'multipart' => $this->prepareMultipartData(),
                        'headers'   => $this->prepareHeaders(),
                        'timeout'   => $this->testConfig['request_timeout'],
                        'on_stats'  => function ($stats) use ($i) {
                            $this->recordRequestStats($i, $stats, microtime(true));
                        }
                    ]);
                };
            }
        };

        $this->info("同时发起 {$totalRequests} 个并发请求，最大并发数为 {$concurrent} ...");

        $pool = new Pool($this->httpClient, $requests($totalRequests), [
            'concurrency' => $concurrent,
            'fulfilled'   => function ($response, $index) use (&$results) {
                $requestId = $index + 1;
                $results[] = [
                    'request_id'      => $requestId,
                    'success'         => true,
                    'status_code'     => $response->getStatusCode(),
                    'duration'        => $this->performanceMetrics[$requestId]['total_time'] ?? 0,
                    'response_size'   => strlen($response->getBody()),
                    'connect_time'    => $this->performanceMetrics[$requestId]['connect_time'] ?? 0,
                    'first_byte_time' => $this->performanceMetrics[$requestId]['starttransfer_time'] ?? 0,
                ];
            },
            'rejected'    => function ($reason, $index) use (&$results) {
                $errorMsg  = $reason instanceof \Exception ? $reason->getMessage() : (string) $reason;
                $results[] = [
                    'request_id' => $index + 1,
                    'success'    => false,
                    'error'      => $errorMsg,
                    'duration'   => 0,
                ];
            },
        ]);

        // 等待所有请求完成
        $promise = $pool->promise();
        $promise->wait();

        $endTime       = microtime(true);
        $totalDuration = $endTime - $startTime;

        $this->analyzeResults($results, $totalDuration, 'Guzzle Pool-全并发');
    }

    /**
     * 渐进并发测试 - 逐步增加并发数找到性能拐点
     */
    private function runProgressiveConcurrentTest(): void
    {
        $this->info('📈 渐进并发模式 - 寻找性能拐点');

        $maxConcurrent   = $this->testConfig['concurrent'];
        $requestsPerStep = 20; // 每个并发级别测试20个请求
        $url             = $this->testConfig['server'].$this->testConfig['api_endpoint'];

        $progressiveResults = [];

        for ($concurrent = 1; $concurrent <= $maxConcurrent; $concurrent++) {
            $this->info("测试并发数: {$concurrent}");

            $stepResults                     = $this->executeProgressiveStep($concurrent, $requestsPerStep, $url);
            $progressiveResults[$concurrent] = $stepResults;

            // 显示当前并发级别的性能
            $avgTime = $stepResults['duration'];
            $qps     = $stepResults['qps'];
            $this->info("  并发{$concurrent}: QPS=".round($qps, 2).", 平均响应时间=".round($avgTime, 3)."秒");
        }

        $this->analyzeProgressiveResults($progressiveResults);
    }

    /**
     * 方法2: 使用cURL Multi实现真正并发
     */
    private function runCurlMultiTest(): void
    {
        $this->info('🔥 使用cURL Multi进行并发测试');

        $concurrent    = $this->testConfig['concurrent'];
        $totalRequests = $this->testConfig['requests'];
        $url           = $this->testConfig['server'].$this->testConfig['api_endpoint'];

        $startTime = microtime(true);
        $results   = [];

        // 分批处理
        $batches = array_chunk(range(1, $totalRequests), $concurrent);

        foreach ($batches as $batchIndex => $batch) {
            $this->info("执行第".($batchIndex + 1)."批cURL并发请求");

            $batchStartTime = microtime(true);
            $multiHandle    = curl_multi_init();
            $curlHandles    = [];

            // 创建并发cURL句柄
            foreach ($batch as $requestId) {
                $ch = curl_init();
                curl_setopt_array($ch, [
                    CURLOPT_URL            => $url,
                    CURLOPT_POST           => true,
                    CURLOPT_POSTFIELDS     => $this->prepareCurlPostData(),
                    CURLOPT_HTTPHEADER     => $this->prepareCurlHeaders(),
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_TIMEOUT        => $this->testConfig['request_timeout'],
                    CURLOPT_CONNECTTIMEOUT => $this->testConfig['connect_timeout'],
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_FOLLOWLOCATION => true,
                ]);

                curl_multi_add_handle($multiHandle, $ch);
                $curlHandles[$requestId] = $ch;
            }

            // 执行并发请求
            $running = null;
            do {
                curl_multi_exec($multiHandle, $running);
                curl_multi_select($multiHandle);
            } while ($running > 0);

            $batchEndTime  = microtime(true);
            $batchDuration = $batchEndTime - $batchStartTime;

            // 处理结果
            foreach ($curlHandles as $requestId => $ch) {
                $response = curl_multi_getcontent($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error    = curl_error($ch);

                $results[] = [
                    'request_id'    => $requestId,
                    'success'       => empty($error) && $httpCode >= 200 && $httpCode < 300,
                    'status_code'   => $httpCode,
                    'duration'      => $batchDuration,
                    'response_size' => strlen($response),
                    'error'         => $error ?: null,
                ];

                curl_multi_remove_handle($multiHandle, $ch);
                curl_close($ch);
            }

            curl_multi_close($multiHandle);

            $this->info("cURL批次完成，耗时: ".round($batchDuration, 3)."秒");
        }

        $endTime       = microtime(true);
        $totalDuration = $endTime - $startTime;

        $this->analyzeResults($results, $totalDuration, 'cURL Multi');
    }

    /**
     * 执行批次请求 - 带详细性能监控
     */
    private function executeBatchRequests(array $batch, string $url): array
    {
        $promises       = [];
        $batchStartTime = microtime(true);
        $results        = [];

        // 创建并发Promise
        foreach ($batch as $requestId) {
            $requestStartTime = microtime(true);

            $promises[$requestId] = $this->httpClient->postAsync($url, [
                'multipart' => $this->prepareMultipartData(),
                'headers'   => $this->prepareHeaders(),
                'timeout'   => $this->testConfig['request_timeout'],
                'on_stats'  => function ($stats) use ($requestId, $requestStartTime) {
                    $this->recordRequestStats($requestId, $stats, $requestStartTime);
                }
            ]);
        }

        // 等待所有并发请求完成
        try {
            $responses     = Promise\Utils::settle($promises)->wait();
            $batchEndTime  = microtime(true);
            $batchDuration = $batchEndTime - $batchStartTime;

            // 处理批次结果
            foreach ($responses as $requestId => $response) {
                $result    = $this->processRequestResult($requestId, $response, $batchDuration);
                $results[] = $result;
            }

            $this->info("批次完成，耗时: ".round($batchDuration, 3)."秒");

            // 检测异常批次
            if ($batchDuration > 5.0) { // 超过5秒认为异常
                $this->warn("⚠️  检测到异常批次，耗时: ".round($batchDuration, 3)."秒");
            }
        } catch (\Exception $e) {
            $this->error("批次执行失败: ".$e->getMessage());
        }

        return $results;
    }

    /**
     * 记录请求统计信息
     */
    private function recordRequestStats(int $requestId, $stats, float $requestStartTime): void
    {
        $transferTime = $stats->getTransferTime();
        $handlerStats = $stats->getHandlerStats();

        $this->performanceMetrics[$requestId] = [
            'request_start_time' => $requestStartTime,
            'connect_time'       => $handlerStats['connect_time'] ?? 0,
            'namelookup_time'    => $handlerStats['namelookup_time'] ?? 0,
            'pretransfer_time'   => $handlerStats['pretransfer_time'] ?? 0,
            'starttransfer_time' => $handlerStats['starttransfer_time'] ?? 0,
            'total_time'         => $transferTime,
        ];
    }

    private function prepareMultipartData(): array
    {
        // 随机选择测试图片
        $images = [
            '2025/06/11/9632608f0ead3ab50e829c6773e8ace0.png',
            '2025/06/09/6f906d9097c4ad3f7d75f38ebaf7cc72.png',
            '2025/06/09/a29b5b532fce7125e06ec97ccdf2b168.png',
        ];

        return [
            [
                'name'     => 'color_type',
                'contents' => '3'
            ],
            [
                'name'     => 'subject_id',
                'contents' => '15'  // 使用有效的subject_id
            ],
            [
                'name'     => 'latitude',
                'contents' => '39.9042'
            ],
            [
                'name'     => 'longitude',
                'contents' => '116.4074'
            ],
            [
                'name'     => 'cover',
                'contents' => $images[array_rand($images)]
            ]
        ];
    }

    private function prepareHeaders(): array
    {
        $headers = [
            'Accept'         => 'application/json',
            'User-Agent'     => 'HTTP-Concurrent-Test/1.0',
            'X-Device-Id'    => 'HTTP-Test-Tool',
            'X-Device-Type'  => 'H5',
            'X-Request-Time' => time(),
        ];

        if ($this->testConfig['token']) {
            $headers['Authorization'] = 'Bearer '.$this->testConfig['token'];
        }

        return $headers;
    }

    private function prepareCurlPostData(): string
    {
        // 随机选择测试图片
        $images = [
            '2025/06/11/9632608f0ead3ab50e829c6773e8ace0.png',
            '2025/06/09/6f906d9097c4ad3f7d75f38ebaf7cc72.png',
            '2025/06/09/a29b5b532fce7125e06ec97ccdf2b168.png',
        ];

        return http_build_query([
            'color_type' => '3',  // 统一使用color_type=3
            'subject_id' => '15',  // 使用有效的subject_id
            'latitude'   => '39.9042',
            'longitude'  => '116.4074',
            'cover'      => $images[array_rand($images)]
        ]);
    }

    private function prepareCurlHeaders(): array
    {
        $headers = [
            'Accept: application/json',
            'User-Agent: HTTP-Concurrent-Test/1.0',
            'X-Device-Id: HTTP-Test-Tool',
            'X-Device-Type: H5',
            'X-Request-Time: '.time(),
            'Content-Type: application/x-www-form-urlencoded',
        ];

        if ($this->testConfig['token']) {
            $headers[] = 'Authorization: Bearer '.$this->testConfig['token'];
        }

        return $headers;
    }

    /**
     * 处理单个请求结果
     */
    private function processRequestResult(int $requestId, array $response, float $batchDuration): array
    {
        if ($response['state'] === 'fulfilled') {
            $httpResponse = $response['value'];
            return [
                'request_id'      => $requestId,
                'success'         => true,
                'status_code'     => $httpResponse->getStatusCode(),
                'duration'        => $this->performanceMetrics[$requestId]['total_time'] ?? $batchDuration,
                'response_size'   => strlen($httpResponse->getBody()),
                'connect_time'    => $this->performanceMetrics[$requestId]['connect_time'] ?? 0,
                'first_byte_time' => $this->performanceMetrics[$requestId]['starttransfer_time'] ?? 0,
            ];
        } else {
            return [
                'request_id'  => $requestId,
                'success'     => false,
                'error'       => $response['reason']->getMessage(),
                'duration'    => $batchDuration,
                'retry_count' => 0, // TODO: 实现重试逻辑
            ];
        }
    }

    /**
     * 处理全并发结果
     */
    private function processFullConcurrentResults(array $responses, float $totalDuration): array
    {
        $results = [];
        foreach ($responses as $requestId => $response) {
            $results[] = $this->processRequestResult($requestId, $response, $totalDuration);
        }
        return $results;
    }

    /**
     * 执行渐进测试步骤
     */
    private function executeProgressiveStep(int $concurrent, int $requests, string $url): array
    {
        $promises  = [];
        $startTime = microtime(true);

        for ($i = 1; $i <= $requests; $i++) {
            $promises[$i] = $this->httpClient->postAsync($url, [
                'multipart' => $this->prepareMultipartData(),
                'headers'   => $this->prepareHeaders(),
                'timeout'   => $this->testConfig['request_timeout'],
            ]);

            // 控制并发数
            if (count($promises) >= $concurrent) {
                $responses = Promise\Utils::settle($promises)->wait();
                $promises  = [];
            }
        }

        // 处理剩余请求
        if (! empty($promises)) {
            $responses = Promise\Utils::settle($promises)->wait();
        }

        $endTime = microtime(true);
        return [
            'concurrent' => $concurrent,
            'requests'   => $requests,
            'duration'   => $endTime - $startTime,
            'qps'        => $requests / ($endTime - $startTime),
        ];
    }

    /**
     * 预热请求
     */
    private function runWarmupRequests(): void
    {
        $warmupCount = $this->testConfig['warmup'];
        $this->info("🔥 执行预热请求 ({$warmupCount} 个)...");

        $url      = $this->testConfig['server'].$this->testConfig['api_endpoint'];
        $promises = [];

        for ($i = 1; $i <= $warmupCount; $i++) {
            $promises[$i] = $this->httpClient->postAsync($url, [
                'multipart' => $this->prepareMultipartData(),
                'headers'   => $this->prepareHeaders(),
                'timeout'   => $this->testConfig['request_timeout'],
            ]);
        }

        try {
            Promise\Utils::settle($promises)->wait();
            $this->info("✅ 预热完成");
        } catch (\Exception $e) {
            $this->warn("⚠️  预热请求部分失败: ".$e->getMessage());
        }
    }

    private function analyzeResults(array $results, float $totalTime, string $method): void
    {
        $totalRequests = count($results);
        $successCount  = count(array_filter($results, fn($r) => $r['success']));
        $successRate   = round(($successCount / $totalRequests) * 100, 1);

        $successResults = array_filter($results, fn($r) => $r['success']);

        // 详细性能统计
        $durations   = array_column($successResults, 'duration');
        $avgDuration = $durations ? round(array_sum($durations) / count($durations), 3) : 0;
        $minDuration = $durations ? round(min($durations), 3) : 0;
        $maxDuration = $durations ? round(max($durations), 3) : 0;

        // 计算标准差和变异系数
        $stdDev                 = $this->calculateStandardDeviation($durations);
        $coefficientOfVariation = $avgDuration > 0 ? round(($stdDev / $avgDuration) * 100, 1) : 0;

        $qps = round($successCount / $totalTime, 2);

        $this->info("📊 === {$method}并发测试结果 === 📊");
        $this->table([
            '指标', '值'
        ], [
            ['测试方法', $method],
            ['总请求数', $totalRequests],
            ['成功请求', $successCount],
            ['成功率', $successRate.'%'],
            ['总耗时', round($totalTime, 3).'秒'],
            ['平均响应时间', $avgDuration.'秒'],
            ['最小响应时间', $minDuration.'秒'],
            ['最大响应时间', $maxDuration.'秒'],
            ['响应时间标准差', round($stdDev, 3).'秒'],
            ['变异系数', $coefficientOfVariation.'%'],
            ['QPS', $qps],
            ['并发数', $this->testConfig['concurrent']],
        ]);

        // 性能波动分析
        $this->analyzePerformanceFluctuation($durations, $avgDuration, $stdDev);

        // 显示失败的请求
        $failedResults = array_filter($results, fn($r) => ! $r['success']);
        if (! empty($failedResults)) {
            $this->warn("❌ 失败请求详情:");
            foreach (array_slice($failedResults, 0, 5) as $failed) {
                $this->warn("  请求#{$failed['request_id']}: ".($failed['error'] ?? '未知错误'));
            }
            if (count($failedResults) > 5) {
                $this->warn("  ... 还有 ".(count($failedResults) - 5)." 个失败请求");
            }
        }

        // 性能建议
        $this->providePerformanceRecommendations($successRate, $avgDuration, $coefficientOfVariation, $qps);
    }

    /**
     * 计算标准差
     */
    private function calculateStandardDeviation(array $values): float
    {
        if (empty($values)) {
            return 0;
        }

        $mean               = array_sum($values) / count($values);
        $squaredDifferences = array_map(fn($x) => pow($x - $mean, 2), $values);
        $variance           = array_sum($squaredDifferences) / count($values);

        return sqrt($variance);
    }

    /**
     * 性能波动分析
     */
    private function analyzePerformanceFluctuation(array $durations, float $avgDuration, float $stdDev): void
    {
        if (empty($durations)) {
            return;
        }

        $this->info("\n🔍 === 性能波动分析 === 🔍");

        // 识别异常请求（超过平均值+2倍标准差）
        $threshold = $avgDuration + (2 * $stdDev);
        $anomalies = array_filter($durations, fn($d) => $d > $threshold);

        if (! empty($anomalies)) {
            $this->warn("⚠️  检测到 ".count($anomalies)." 个异常慢请求 (>{$threshold}秒)");
            $this->warn("   异常请求响应时间: ".implode(', ',
                    array_map(fn($d) => round($d, 3).'s', array_slice($anomalies, 0, 5))));
        }

        // 性能稳定性评估
        $coefficientOfVariation = $avgDuration > 0 ? ($stdDev / $avgDuration) * 100 : 0;

        if ($coefficientOfVariation < 10) {
            $this->info("✅ 性能稳定性: 优秀 (变异系数: ".round($coefficientOfVariation, 1)."%)");
        } elseif ($coefficientOfVariation < 25) {
            $this->warn("⚠️  性能稳定性: 一般 (变异系数: ".round($coefficientOfVariation, 1)."%)");
        } else {
            $this->error("❌ 性能稳定性: 较差 (变异系数: ".round($coefficientOfVariation, 1)."%)");
        }
    }

    /**
     * 提供性能优化建议
     */
    private function providePerformanceRecommendations(
        float $successRate,
        float $avgDuration,
        float $coefficientOfVariation,
        float $qps
    ): void {
        $this->info("\n💡 === 性能优化建议 === 💡");

        if ($successRate < 95) {
            $this->warn("🔧 成功率偏低 ({$successRate}%)，建议:");
            $this->warn("   - 检查服务器资源使用情况");
            $this->warn("   - 增加超时时间或优化接口性能");
            $this->warn("   - 检查网络连接稳定性");
        }

        if ($avgDuration > 3.0) {
            $this->warn("🐌 平均响应时间较慢 ({$avgDuration}秒)，建议:");
            $this->warn("   - 优化数据库查询");
            $this->warn("   - 检查Python图像处理服务性能");
            $this->warn("   - 考虑增加服务器资源");
        }

        if ($coefficientOfVariation > 25) {
            $this->warn("📊 响应时间波动较大 ({$coefficientOfVariation}%)，建议:");
            $this->warn("   - 检查服务器负载均衡配置");
            $this->warn("   - 优化数据库连接池");
            $this->warn("   - 检查是否存在资源竞争");
        }

        if ($qps < 2.0) {
            $this->warn("⚡ QPS较低 ({$qps})，建议:");
            $this->warn("   - 优化接口逻辑，减少不必要的计算");
            $this->warn("   - 考虑使用缓存机制");
            $this->warn("   - 升级服务器硬件配置");
        }

        $this->info("✨ 总体建议:");
        $this->info("   - 定期进行性能测试，监控性能趋势");
        $this->info("   - 在生产环境中设置性能监控告警");
        $this->info("   - 考虑实施APM(应用性能监控)解决方案");
    }

    /**
     * 分析渐进测试结果
     */
    private function analyzeProgressiveResults(array $progressiveResults): void
    {
        $this->info("\n📈 === 渐进并发测试结果 === 📈");

        $tableData          = [];
        $bestQPS            = 0;
        $optimalConcurrency = 1;

        foreach ($progressiveResults as $concurrent => $result) {
            $tableData[] = [
                $concurrent,
                round($result['qps'], 2),
                round($result['duration'], 3).'秒'
            ];

            if ($result['qps'] > $bestQPS) {
                $bestQPS            = $result['qps'];
                $optimalConcurrency = $concurrent;
            }
        }

        $this->table(['并发数', 'QPS', '平均响应时间'], $tableData);

        $this->info("🎯 最优并发数: {$optimalConcurrency} (QPS: {$bestQPS})");
        $this->info("💡 建议在生产环境中使用 {$optimalConcurrency} 左右的并发数");
    }

    /**
     * 保存详细报告
     */
    private function saveDetailedReport(): void
    {
        $reportFile = $this->option('save-report');
        $timestamp  = date('Y-m-d H:i:s');

        $report = [
            'timestamp'           => $timestamp,
            'config'              => $this->testConfig,
            'performance_metrics' => $this->performanceMetrics,
            'summary'             => [
                'test_completed'      => true,
                'report_generated_at' => $timestamp
            ]
        ];

        try {
            file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            $this->info("📄 详细报告已保存到: {$reportFile}");
        } catch (\Exception $e) {
            $this->error("保存报告失败: ".$e->getMessage());
        }
    }
}