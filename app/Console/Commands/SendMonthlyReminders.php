<?php

namespace App\Console\Commands;

use App\Models\UserReminderSetting;
use App\Notifications\MonthlyReminderNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendMonthlyReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:send-monthly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '发送每月复检提醒';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始发送每月复检提醒...');

        $today        = now();
        $currentDay   = $today->day;
        $successCount = 0;
        $failCount    = 0;

        $this->info("检查今天({$currentDay}号)需要提醒的用户...");

        // 直接流式处理，避免内存溢出
        UserReminderSetting::where('is_enabled', 1)
            ->where('reminder_day', $currentDay)
            ->whereHas('user.info', function ($query) {
                $query->where('mini_notify', 1);
            })
            // 过滤掉本月已提醒的用户
            ->where(function ($query) use ($today) {
                $query->whereNull('last_reminded_at')
                    ->orWhere(function ($q) use ($today) {
                        $q->whereYear('last_reminded_at', '!=', $today->year)
                            ->orWhereMonth('last_reminded_at', '!=', $today->month);
                    });
            })
            // 过滤掉今天创建的提醒设置（避免刚检查完就提醒）
            ->whereDate('created_at', '!=', $today->toDateString())
            ->with(['user.info', 'subject'])
            ->chunk(100, function ($settings) use (&$successCount, &$failCount) {
                foreach ($settings as $setting) {
                    $this->info("正在处理用户 {$setting->user_id}...");

                    // 发送提醒到队列（队列会自动处理重试）
                    if ($this->sendReminderToUser($setting)) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                }
            });

        if ($successCount === 0 && $failCount === 0) {
            $this->info('今天没有需要发送提醒的用户');
            return;
        }

        $this->info("智能提醒发送完成！成功: {$successCount}, 失败: {$failCount}");
    }

    /**
     * 发送提醒给用户
     */
    private function sendReminderToUser($setting): bool
    {
        try {
            $user    = $setting->user;
            $subject = $setting->subject;

            // 创建通知
            $notification = new MonthlyReminderNotification(
                $subject->name ?? null,
                'abnormal_followup',
                $setting->id
            );

            // 发送通知到队列（队列会自动处理重试）
            $user->notify($notification);
            $subjectInfo = $subject ? "（{$subject->name}）" : '';
            $this->info("已向用户 {$user->id}{$subjectInfo} 发送异常复检提醒到队列");

            return true;
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $this->error("向用户 {$setting->user_id} 发送提醒失败: {$errorMessage}");

            Log::error('发送智能提醒失败', [
                'user_id'    => $setting->user_id,
                'subject_id' => $setting->subject_id,
                'error'      => $errorMessage,
            ]);

            return false;
        }
    }

}
