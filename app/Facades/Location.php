<?php

namespace App\Facades;

use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Facade;

class Location extends Facade
{
    public static function addressToLocation(string $location): array
    {
        try {
            $response = self::request('GET', 'https://apis.map.qq.com/ws/geocoder/v1/', [
                'address' => $location,
                'key'     => env('TENCENT_MAP_API_KEY')
            ]);

            if ($response['status'] != 0) {
                throw new \Exception($response['message']);
            }

            return $response['result'];
        } catch (Exception $e) {
            throw new Exception('经纬度转换接口服务器访问异常【'.$e->getMessage().'】');
        }
    }

    /**
     * 根据经纬度获取位置
     *
     * @param  float  $lat
     * @param  float  $lng
     * @return array
     * @throws \Exception
     */
    public static function locationToAddress(float $lat, float $lng): array
    {
        try {
            $response = self::request('GET', 'https://apis.map.qq.com/ws/geocoder/v1/', [
                'location' => $lat.','.$lng,
                'key'      => env('TENCENT_MAP_API_KEY')
            ]);

            if ($response['status'] != 0) {
                throw new \Exception('接口服务器访问异常【'.$response['message'].'】');
            }
            return $response['result'];
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public static function request($method, $url, $data)
    {
        try {
            $client   = new Client();
            $response = $client->request($method, $url, [
                'query' => $data,
            ]);

            $statusCode = $response->getStatusCode();

            if ($statusCode == 200 || $statusCode == 201 || $statusCode == 0) {
                return json_decode($response->getBody()->getContents(), true);
            } elseif ($statusCode == 400) {
                throw new \Exception("返回内容错误【Un】");
            } else {
                throw new \Exception("接口服务器访问异常【{$response->getStatusCode()}】");
            }
        } catch (\Exception $exception) {
            throw new \Exception("接口服务器访问异常【{$response->getStatusCode()}】");
        }
    }

}